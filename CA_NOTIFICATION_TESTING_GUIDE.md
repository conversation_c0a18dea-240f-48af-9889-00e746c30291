# CA/CS/EHK Train Journey Notification Testing Guide

## Overview

This guide provides comprehensive instructions for testing the CA/CS/EHK train journey notification system with all required scenarios.

## Test Configuration

**Firebase Configuration:**
- Account: <EMAIL>
- Project: RailwaysApp-Prod (ID: railwaysapp-prod)
- Custom sound: railops_alarm
- Notification channels: railops_alerts

**Test Route:** ARA → BTA → DNR → PNBE → RJPB → PNC

**Test Coordinates:**
- ARA: 25.5500, 84.6667
- BTA: 25.2167, 84.3667
- DNR: 25.4167, 85.0167
- PNBE: 25.5941, 85.1376
- RJPB: 25.6093, 85.1947
- PNC: 25.6167, 85.2167
- New Delhi (fallback): 28.6139, 77.2090

## Test Scenarios

### Scenario 1: Multi-Station Proximity Testing
**Route:** ARA → BTA → DNR → PNBE → RJPB → PNC

**Test Cases:**
1. **ARA and BTA both within 50km** ✅ Send notifications for both
2. **BTA and DNR proximity with anti-duplication:**
   - 🚫 No notification for BTA if no passenger detail changes
   - ✅ Send notification for BTA if passenger details changed
   - ✅ Send notification for DNR (first time)

### Scenario 2: Multi-Coach Assignment Testing
**CA assigned to coaches A1 and B3**

**Expected notification format:**
```
StationCode | Coach | Onboarding (green) | Deboarding (orange) | Vacant (grey)
DDU         | A1    | 5                  | 3                   | 2
DDU         | B3    | 6                  | 3                   | 6
```

**Notification status tracking:** unread → read → clear

### Scenario 3: No Passenger Activity Testing
**When no passengers are boarding/deboarding at stations**

**Expected simplified notification:** "No passenger onboarding/deboarding at station PNBE, DNR"

## How to Run Tests

### Method 1: Manual Testing Interface (Recommended)

1. **Navigate to CA Notification Testing Screen:**
   ```dart
   Navigator.push(
     context,
     MaterialPageRoute(builder: (context) => CANotificationTestingScreen()),
   );
   ```

2. **Configure Test Parameters:**
   - Select test station from dropdown
   - Set train number (default: TEST12345)
   - Set CA user ID (default: test_ca_001)
   - Select coaches (default: A1, B3)

3. **Run Individual Scenarios:**
   - **Scenario 1:** Tap "Test Full Route" or "Test Anti-Duplication"
   - **Scenario 2:** Tap "Test Multi-Coach Notification"
   - **Scenario 3:** Tap "Test No Activity Notification"

4. **Quick Tests:**
   - **Single Station:** Test current station with selected configuration
   - **FCM Token:** Verify FCM token generation and Firestore sync

### Method 2: Programmatic Testing

```dart
import 'package:railops/utils/ca_notification_test_runner.dart';

// Run complete test suite
final results = await CANotificationTestRunner.executeCompleteTestSuite(
  trainNumber: 'TEST12345',
  userId: 'test_ca_001',
  coaches: ['A1', 'B3'],
  includeEndToEndTests: true,
);

// Run quick test
final quickResult = await CANotificationTestRunner.runQuickTest(
  station: 'NEW_DELHI',
  trainNumber: 'QUICK_TEST',
  userId: 'quick_test_user',
);
```

### Method 3: Unit Testing

```bash
# Run CA/CS/EHK specific tests
flutter test test/integration/ca_cs_ehk_notification_test.dart

# Run all notification integration tests
flutter test test/integration/
```

## Real Android Device Testing

### Prerequisites
1. **Android device** with Firebase Cloud Messaging enabled
2. **Internet connection** for API calls
3. **Location permissions** enabled
4. **Notification permissions** granted

### Testing Steps

1. **Install and run the app** on Android device
2. **Enable location services** and grant permissions
3. **Navigate to CA Notification Testing Screen**
4. **Run each test scenario** and verify:
   - Console logs show correct status messages
   - Notifications appear in device notification tray
   - Notification sound (railops_alarm) plays
   - Notification content matches expected format

### Expected Console Output

```
🧪 Testing Multi-Station Proximity: ARA → BTA → DNR → PNBE → RJPB → PNC
📍 ARA: sent - Enhanced notification sent successfully
📍 BTA: sent - Enhanced notification sent successfully
📍 DNR: sent - Enhanced notification sent successfully
📍 PNBE: sent - Enhanced notification sent successfully
📍 RJPB: sent - Enhanced notification sent successfully
📍 PNC: sent - Enhanced notification sent successfully
✅ Multi-Station Proximity Test Completed!
```

### Verification Checklist

- [ ] **Multi-station notifications** sent for all stations in route
- [ ] **Anti-duplication logic** prevents repeat notifications
- [ ] **Multi-coach format** displays correct table structure
- [ ] **No activity notifications** show simplified message
- [ ] **FCM tokens** generated and synced to Firestore
- [ ] **Firebase Cloud Functions** /notify endpoint responding
- [ ] **Notification tray** shows unread/read/clear status
- [ ] **Custom sound** (railops_alarm) plays on notification
- [ ] **Notification channels** (railops_alerts) configured correctly

## Debugging

### Console Debugging
All tests include detailed console logging with prefixes:
- `🧪` Test execution
- `📍` Location/station updates
- `✅` Success messages
- `❌` Error messages
- `📊` Data formatting
- `🔧` Configuration updates

### Common Issues

1. **FCM Token Issues:**
   ```dart
   // Check token generation
   final token = await FcmTokenService.getFreshFcmToken();
   print('FCM Token: $token');
   ```

2. **Firestore Sync Issues:**
   ```dart
   // Verify Firestore connectivity
   final sync = await FcmTokenService.ensureTokenSync('test_token');
   print('Firestore sync: $sync');
   ```

3. **Cloud Function Issues:**
   ```bash
   # Check Firebase Cloud Function logs
   firebase functions:log --only notify
   ```

## Test Results Interpretation

### Success Criteria
- **Status: 'sent'** - Notification successfully delivered
- **Status: 'skipped'** - Correctly skipped due to anti-duplication
- **Status: 'error'** - Failed (requires investigation)

### Performance Metrics
- **Notification delivery time** < 5 seconds
- **FCM token generation** < 2 seconds
- **Firestore sync** < 3 seconds
- **API response time** < 10 seconds

## Integration with Existing Systems

### Location Service Integration
The notification system integrates with existing location services in:
- `lib/services/location_services/location_services.dart`
- `lib/utils/fetch_location.dart`

### Firebase Integration
- **Cloud Functions:** `functions/src/index.ts` (/notify endpoint)
- **FCM Service:** `lib/services/firebase_messaging_service.dart`
- **Token Management:** `lib/services/fcm_token_service.dart`

### Notification Services
- **Core Service:** `lib/services/notification_services/onboarding_notification_service.dart`
- **Integration Helper:** `lib/services/notification_services/notification_integration_helper.dart`
- **CA Test Service:** `lib/services/notification_services/ca_notification_test_service.dart`

## Support

For issues or questions:
1. Check console logs for detailed error messages
2. Verify Firebase project configuration
3. Ensure all permissions are granted
4. Test with simplified scenarios first
5. Use the manual testing interface for step-by-step debugging

## Next Steps

After successful testing:
1. **Deploy to production** with verified configuration
2. **Monitor notification delivery** rates and performance
3. **Collect user feedback** on notification timing and content
4. **Optimize based on real-world usage** patterns
5. **Expand testing** to additional train routes and scenarios
