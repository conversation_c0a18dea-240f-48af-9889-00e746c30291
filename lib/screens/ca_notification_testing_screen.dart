import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firestore_token_service.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';

/// Manual testing interface for CA/CS/EHK train journey notifications
///
/// Features:
/// - Manual button triggers with real coordinates
/// - Console-based debugging with detailed logging
/// - UI feedback with timestamps
/// - Multi-station proximity testing
/// - Multi-coach assignment testing
/// - Anti-duplication logic verification
/// - No passenger activity testing
class CANotificationTestingScreen extends StatefulWidget {
  const CANotificationTestingScreen({super.key});

  @override
  State<CANotificationTestingScreen> createState() =>
      _CANotificationTestingScreenState();
}

class _CANotificationTestingScreenState
    extends State<CANotificationTestingScreen> {
  bool _isLoading = false;
  String _lastTestResult = '';
  final Map<String, String> _testCoordinates = {
    'New Delhi': '28.6139,77.2090',
    'ARA': '25.5500,84.6667',
    'BTA': '25.2167,84.3667',
    'DNR': '25.4167,85.0167',
    'PNBE': '25.5941,85.1376',
    'RJPB': '25.6093,85.1947',
    'PNC': '25.6167,85.2167',
  };

  String _selectedStation = 'New Delhi';
  String _trainNumber = 'TEST12345';
  String _userId = 'test_ca_001';
  final List<String> _selectedCoaches = ['A1', 'B3'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CA/CS/EHK Notification Testing'),
        backgroundColor: Colors.orange.shade50,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildConfigurationCard(),
            const SizedBox(height: 16),
            _buildScenario1TestsCard(),
            const SizedBox(height: 16),
            _buildScenario2TestsCard(),
            const SizedBox(height: 16),
            _buildScenario3TestsCard(),
            const SizedBox(height: 16),
            _buildQuickTestsCard(),
            const SizedBox(height: 16),
            _buildTestResultsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Configuration',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: _selectedStation,
              decoration: const InputDecoration(labelText: 'Test Station'),
              items: _testCoordinates.keys.map((station) {
                return DropdownMenuItem(value: station, child: Text(station));
              }).toList(),
              onChanged: (value) => setState(() => _selectedStation = value!),
            ),
            const SizedBox(height: 8),
            TextFormField(
              initialValue: _trainNumber,
              decoration: const InputDecoration(labelText: 'Train Number'),
              onChanged: (value) => _trainNumber = value,
            ),
            const SizedBox(height: 8),
            TextFormField(
              initialValue: _userId,
              decoration: const InputDecoration(labelText: 'CA User ID'),
              onChanged: (value) => _userId = value,
            ),
            const SizedBox(height: 8),
            Text('Selected Coaches: ${_selectedCoaches.join(', ')}'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ['A1', 'A2', 'B1', 'B2', 'B3', 'C1', 'C2'].map((coach) {
                return FilterChip(
                  label: Text(coach),
                  selected: _selectedCoaches.contains(coach),
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedCoaches.add(coach);
                      } else {
                        _selectedCoaches.remove(coach);
                      }
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScenario1TestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scenario 1: Multi-Station Proximity Testing',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('Route: ARA → BTA → DNR → PNBE → RJPB → PNC'),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed:
                        _isLoading ? null : () => _testMultiStationProximity(),
                    child: const Text('Test Full Route'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testAntiDuplication(),
                    child: const Text('Test Anti-Duplication'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScenario2TestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scenario 2: Multi-Coach Assignment Testing',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
                'Expected format: StationCode | Coach | Onboarding | Deboarding | Vacant'),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _isLoading ? null : () => _testMultiCoachAssignment(),
              child: const Text('Test Multi-Coach Notification'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScenario3TestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scenario 3: No Passenger Activity Testing',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
                'Test simplified notifications when no passengers boarding/deboarding'),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _isLoading ? null : () => _testNoPassengerActivity(),
              child: const Text('Test No Activity Notification'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickTestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Tests',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testSingleStation(),
                    child: const Text('Test Current Station'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testFCMToken(),
                    child: const Text('Test FCM Token'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Results',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_lastTestResult.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _lastTestResult,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              )
            else
              const Text(
                  'No test results yet. Run a test to see results here.'),
          ],
        ),
      ),
    );
  }

  Future<void> _testMultiStationProximity() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing multi-station proximity...';
    });

    try {
      if (kDebugMode) {
        print(
            '🧪 Testing Multi-Station Proximity: ARA → BTA → DNR → PNBE → RJPB → PNC');
      }

      final stations = ['ARA', 'BTA', 'DNR', 'PNBE', 'RJPB', 'PNC'];
      final results = <String, Map<String, dynamic>>{};

      for (final station in stations) {
        final coordinates = _testCoordinates[station]?.split(',');
        if (coordinates != null && coordinates.length == 2) {
          final result = await _callNotifyEndpoint(
            station: station,
            lat: coordinates[0],
            lng: coordinates[1],
            passengerData: _generateTestPassengerData(),
          );
          results[station] = result;

          if (kDebugMode) {
            print('📍 $station: ${result['status']} - ${result['message']}');
          }

          // Small delay between calls
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      final successCount =
          results.values.where((r) => r['status'] == 'sent').length;
      final skippedCount =
          results.values.where((r) => r['status'] == 'skipped').length;

      setState(() {
        _lastTestResult = '''✅ Multi-Station Proximity Test Completed!

Route: ${stations.join(' → ')}
Notifications sent: $successCount
Notifications skipped: $skippedCount
Total stations tested: ${stations.length}

Results:
${results.entries.map((e) => '${e.key}: ${e.value['status']}').join('\n')}

Completed at: ${DateTime.now().toString()}''';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Multi-Station Proximity Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Multi-station proximity test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testAntiDuplication() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing anti-duplication logic...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing Anti-Duplication Logic');
      }

      final coordinates = _testCoordinates[_selectedStation]?.split(',');
      if (coordinates == null || coordinates.length != 2) {
        throw Exception('Invalid coordinates for $_selectedStation');
      }

      // First call - should send
      final firstResult = await _callNotifyEndpoint(
        station: _selectedStation,
        lat: coordinates[0],
        lng: coordinates[1],
        passengerData: _generateTestPassengerData(),
      );

      await Future.delayed(const Duration(seconds: 1));

      // Second call with same data - should be skipped
      final secondResult = await _callNotifyEndpoint(
        station: _selectedStation,
        lat: coordinates[0],
        lng: coordinates[1],
        passengerData: _generateTestPassengerData(),
      );

      await Future.delayed(const Duration(seconds: 1));

      // Third call with changed data - should send
      final thirdResult = await _callNotifyEndpoint(
        station: _selectedStation,
        lat: coordinates[0],
        lng: coordinates[1],
        passengerData: _generateChangedPassengerData(),
      );

      setState(() {
        _lastTestResult = '''✅ Anti-Duplication Test Completed!

Station: $_selectedStation
First call: ${firstResult['status']} (expected: sent)
Second call: ${secondResult['status']} (expected: skipped)
Third call: ${thirdResult['status']} (expected: sent)

Anti-duplication logic: ${secondResult['status'] == 'skipped' ? 'WORKING' : 'FAILED'}

Completed at: ${DateTime.now().toString()}''';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Anti-Duplication Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Anti-duplication test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testMultiCoachAssignment() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing multi-coach assignment...';
    });

    try {
      if (kDebugMode) {
        print(
            '🧪 Testing Multi-Coach Assignment for coaches: ${_selectedCoaches.join(', ')}');
      }

      final coordinates = _testCoordinates[_selectedStation]?.split(',');
      if (coordinates == null || coordinates.length != 2) {
        throw Exception('Invalid coordinates for $_selectedStation');
      }

      final result = await _callNotifyEndpoint(
        station: _selectedStation,
        lat: coordinates[0],
        lng: coordinates[1],
        passengerData: _generateMultiCoachPassengerData(),
      );

      setState(() {
        _lastTestResult = '''✅ Multi-Coach Assignment Test Completed!

Station: $_selectedStation
Coaches: ${_selectedCoaches.join(', ')}
Status: ${result['status']}
Message: ${result['message']}

Expected notification format:
StationCode | Coach | Onboarding | Deboarding | Vacant
$_selectedStation | A1 | 5 | 3 | 2
$_selectedStation | B3 | 6 | 3 | 6

Completed at: ${DateTime.now().toString()}''';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Multi-Coach Assignment Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Multi-coach assignment test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testNoPassengerActivity() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing no passenger activity...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing No Passenger Activity Notification');
      }

      final coordinates = _testCoordinates[_selectedStation]?.split(',');
      if (coordinates == null || coordinates.length != 2) {
        throw Exception('Invalid coordinates for $_selectedStation');
      }

      final result = await _callNotifyEndpoint(
        station: _selectedStation,
        lat: coordinates[0],
        lng: coordinates[1],
        passengerData: _generateNoActivityPassengerData(),
      );

      setState(() {
        _lastTestResult = '''✅ No Passenger Activity Test Completed!

Station: $_selectedStation
Status: ${result['status']}
Message: ${result['message']}

Expected: "No passenger onboarding/deboarding at station $_selectedStation"

Completed at: ${DateTime.now().toString()}''';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ No Passenger Activity Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ No passenger activity test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSingleStation() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing single station notification...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing Single Station: $_selectedStation');
      }

      final coordinates = _testCoordinates[_selectedStation]?.split(',');
      if (coordinates == null || coordinates.length != 2) {
        throw Exception('Invalid coordinates for $_selectedStation');
      }

      final result = await _callNotifyEndpoint(
        station: _selectedStation,
        lat: coordinates[0],
        lng: coordinates[1],
        passengerData: _generateTestPassengerData(),
      );

      setState(() {
        _lastTestResult = '''✅ Single Station Test Completed!

Station: $_selectedStation
Coordinates: ${coordinates.join(', ')}
Train: $_trainNumber
User: $_userId
Status: ${result['status']}
Message: ${result['message']}

Completed at: ${DateTime.now().toString()}''';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Single Station Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Single station test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testFCMToken() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing FCM token...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing FCM Token Generation and Storage');
      }

      final token = await FcmTokenService.getFreshFcmToken();
      final firestoreSync = await FirestoreTokenService.syncWithFirestore();

      setState(() {
        _lastTestResult = '''✅ FCM Token Test Completed!

Token available: ${token != null ? 'YES' : 'NO'}
Token length: ${token?.length ?? 0}
Firestore sync: ${firestoreSync ? 'SUCCESS' : 'FAILED'}

Token (first 20 chars): ${token?.substring(0, token.length > 20 ? 20 : token.length) ?? 'N/A'}...

Completed at: ${DateTime.now().toString()}''';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ FCM Token Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ FCM token test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<Map<String, dynamic>> _callNotifyEndpoint({
    required String station,
    required String lat,
    required String lng,
    required Map<String, Map<String, int>> passengerData,
  }) async {
    final fcmToken = await FcmTokenService.getFreshFcmToken() ??
        'test_fcm_token_${DateTime.now().millisecondsSinceEpoch}';

    return await FirebaseCloudFunctionService.callNotifyFunction(
      userId: _userId,
      trainNumber: _trainNumber,
      date: DateTime.now().toIso8601String().split('T')[0],
      lat: lat,
      lng: lng,
      fcmToken: fcmToken,
    );
  }

  Map<String, Map<String, int>> _generateTestPassengerData() {
    final data = <String, Map<String, int>>{};
    for (final coach in _selectedCoaches) {
      data[coach] = {
        'onboarding': 5,
        'deboarding': 3,
        'vacant': 2,
      };
    }
    return data;
  }

  Map<String, Map<String, int>> _generateChangedPassengerData() {
    final data = <String, Map<String, int>>{};
    for (final coach in _selectedCoaches) {
      data[coach] = {
        'onboarding': 7, // Changed
        'deboarding': 4, // Changed
        'vacant': 1, // Changed
      };
    }
    return data;
  }

  Map<String, Map<String, int>> _generateMultiCoachPassengerData() {
    return {
      'A1': {'onboarding': 5, 'deboarding': 3, 'vacant': 2},
      'B3': {'onboarding': 6, 'deboarding': 3, 'vacant': 6},
    };
  }

  Map<String, Map<String, int>> _generateNoActivityPassengerData() {
    final data = <String, Map<String, int>>{};
    for (final coach in _selectedCoaches) {
      data[coach] = {
        'onboarding': 0,
        'deboarding': 0,
        'vacant': 10,
      };
    }
    return data;
  }
}
